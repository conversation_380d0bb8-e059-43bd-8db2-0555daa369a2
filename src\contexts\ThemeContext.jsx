"use client";
import React, { createContext, useContext, useEffect, useState } from "react";

// إنشاء Theme Context - zaki alkholy
const ThemeContext = createContext();

// Hook لاستخدام Theme Context - zaki alkholy
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

// Theme Provider Component - zaki alkholy
export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // تحميل الثيم المحفوظ من localStorage عند بدء التطبيق - zaki alkholy
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem("theme");
      const systemPrefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;

      let shouldBeDark = false;
      if (savedTheme) {
        shouldBeDark = savedTheme === "dark";
      } else {
        shouldBeDark = systemPrefersDark;
      }

      // تطبيق الثيم فوراً لمنع الـ flash
      const htmlElement = document.documentElement;
      if (shouldBeDark) {
        htmlElement.classList.add("dark");
      } else {
        htmlElement.classList.remove("dark");
      }

      setIsDarkMode(shouldBeDark);
    } catch (error) {
      console.error("Error loading theme from localStorage:", error);
      setIsDarkMode(false);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // تطبيق الثيم على الـ HTML element - zaki alkholy
  useEffect(() => {
    if (!isLoaded) return;

    const htmlElement = document.documentElement;

    if (isDarkMode) {
      htmlElement.classList.add("dark");
      // استخدام نفس القيم الموجودة في globals.css للدارك مود
      htmlElement.style.setProperty("--background", "#0a0a0a");
      htmlElement.style.setProperty("--foreground", "#ededed");
      htmlElement.style.setProperty("--primary", "#6366f1");
      htmlElement.style.setProperty("--primary-light", "#8b5cf6");
      htmlElement.style.setProperty("--primary-dark", "#4338ca");
      htmlElement.style.setProperty("--secondary", "#94a3b8");
      htmlElement.style.setProperty("--accent", "#a78bfa");
      htmlElement.style.setProperty("--accent-light", "#c4b5fd");
    } else {
      htmlElement.classList.remove("dark");
      // استخدام نفس القيم الموجودة في globals.css للايت مود
      htmlElement.style.setProperty("--background", "#ffffff");
      htmlElement.style.setProperty("--foreground", "#171717");
      htmlElement.style.setProperty("--primary", "#4f46e5");
      htmlElement.style.setProperty("--primary-light", "#6366f1");
      htmlElement.style.setProperty("--primary-dark", "#3730a3");
      htmlElement.style.setProperty("--secondary", "#64748b");
      htmlElement.style.setProperty("--accent", "#8b5cf6");
      htmlElement.style.setProperty("--accent-light", "#a78bfa");
    }

    // حفظ الثيم في localStorage - zaki alkholy
    try {
      localStorage.setItem("theme", isDarkMode ? "dark" : "light");
    } catch (error) {
      console.error("Error saving theme to localStorage:", error);
    }
  }, [isDarkMode, isLoaded]);

  // مراقبة تغيير نظام التشغيل للثيم - zaki alkholy
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = (e) => {
      // فقط إذا لم يكن المستخدم قد اختار ثيم محدد
      const savedTheme = localStorage.getItem("theme");
      if (!savedTheme) {
        setIsDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  // تبديل الثيم - zaki alkholy
  const toggleTheme = () => {
    setIsDarkMode((prev) => !prev);
  };

  // إعادة تعيين الثيم لإعدادات النظام - zaki alkholy
  const resetToSystemTheme = () => {
    const systemPrefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;
    setIsDarkMode(systemPrefersDark);
    localStorage.removeItem("theme");
  };

  const value = {
    isDarkMode,
    toggleTheme,
    resetToSystemTheme,
    isLoaded,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

export default ThemeContext;
