"use client";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { useEffect, useState } from "react";
import { Provider, useSelector } from "react-redux";
import { store } from "@/store/store";
import AuthProvider from "./_Components/AuthProvider";
import Navbar from "./_Components/Navbar/Navbar";
import useAutoLogout from "@/hooks/useAutoLogout";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ThemeProvider } from "@/contexts/ThemeContext";
import ModernNavbar from "./_Components/(MainpageComponents)/ModernNavbar";
// تحميل الخطوط بشكل غير متزامن
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

function ReduxAutoLogoutWrapper() {
  const token = useSelector((state) => state.auth.token);
  useAutoLogout(token);
  return null;
}

export default function RootLayout({ children }) {
  useEffect(() => {
    // تعطيل Browsing Topics API
    if (typeof window !== "undefined") {
      window.chrome = window.chrome || {};
      window.chrome.privacy = window.chrome.privacy || {};
      window.chrome.privacy.websites = window.chrome.privacy.websites || {};
      window.chrome.privacy.websites.topicsEnabled = false;
    }
  }, []);

  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#ffffff" />
        <title>منصة مُعَلِّمِيّ - تعلم باحتراف من أفضل المعلمين</title>
        <meta
          name="title"
          content="منصة معلمي - تعلم باحتراف من أفضل المعلمين"
        />
        <meta
          name="description"
          content="منصة معلمي تقدم كورسات تعليمية عالية الجودة في مختلف المجالات. احصل على تجربة تعليمية مميزة مع أفضل المعلمين العرب."
        />

        <meta
          name="keywords"
          content="معلمي, mo3lmy, منصة تعليمية, تعليم أونلاين, كورسات, دورات تدريبية, تعلم عن بعد, تعليم رقمي, مدرسين عرب, فيديوهات تعليمية, منصة عربية, تطوير الذات, تعلم البرمجة, كورسات مجانية, تعليم محترف, تعلم مهارات جديدة"
        />

        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://mo3lmy.com/" />
        <meta
          property="og:title"
          content="منصة معلمي - تعلم باحتراف من أفضل المعلمين"
        />
        <meta
          property="og:description"
          content="كورسات ودروس تعليمية من معلمين محترفين في مجالات متعددة. انضم الآن وابدأ رحلتك التعليمية."
        />
        <meta
          property="og:image"
          content="https://mo3lmy.com/images/og-banner.png"
        />

        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://mo3lmy.com/" />
        <meta
          property="twitter:title"
          content="منصة معلمي - تعلم باحتراف من أفضل المعلمين"
        />
        <meta
          property="twitter:description"
          content="كورسات ودروس تعليمية من معلمين محترفين في مجالات متعددة. انضم الآن وابدأ رحلتك التعليمية."
        />
        <meta
          property="twitter:image"
          content="https://mo3lmy.com/images/og-banner.png"
        />
        <link rel="icon" href="/graduation-cap.svg" />

        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          href="https://fonts.googleapis.com/css2?family=Baloo+Bhaijaan+2:wght@800&display=swap"
          rel="stylesheet"
        />

        {/* Script لتطبيق الثيم فوراً قبل تحميل React - zaki alkholy */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  const savedTheme = localStorage.getItem('theme');
                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                  const shouldBeDark = savedTheme ? savedTheme === 'dark' : systemPrefersDark;

                  if (shouldBeDark) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                } catch (e) {
                  // في حالة عدم توفر localStorage
                  console.warn('Could not access localStorage for theme');
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} bg-background text-foreground`}
      >
        <Provider store={store}>
          <ThemeProvider>
            <GoogleOAuthProvider clientId="93943969774-jjl9aj2sk045uo856og1363ku6s5f6op.apps.googleusercontent.com">
              <AuthProvider>
                <ReduxAutoLogoutWrapper /> {/* <Navbar /> */}
                <ModernNavbar />
                <div className="min-h-screen">
                  <main className="mx-auto pt-16 ">{children}</main>
                </div>
              </AuthProvider>
            </GoogleOAuthProvider>
          </ThemeProvider>
        </Provider>
      </body>
    </html>
  );
}
