@tailwind base;
@tailwind components;
@tailwind utilities;

/* استيراد الخطوط العربية */
@import url("https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Baloo+Bhaijaan+2:wght@400..800&display=swap");

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #4f46e5;
  --primary-light: #6366f1;
  --primary-dark: #3730a3;
  --secondary: #64748b;
  --accent: #8b5cf6;
  --accent-light: #a78bfa;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #6366f1 0%, #a78bfa 100%);
}

/* Dark mode styles */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #6366f1;
  --primary-light: #8b5cf6;
  --primary-dark: #4338ca;
  --secondary: #94a3b8;
  --accent: #a78bfa;
  --accent-light: #c4b5fd;
}

/* Additional dark mode styles - zaki alkholy */
.dark body {
  background-color: #0a0a0a;
  color: #ededed;
}

@layer base {
  * {
    @apply box-border p-0 m-0;
  }

  html {
    @apply max-w-full overflow-x-hidden min-h-screen;
    /* إضافة transition سلس للثيم - zaki alkholy */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body {
    @apply max-w-full overflow-x-hidden min-h-screen bg-background text-foreground font-sans leading-normal;
    /* إضافة transition سلس للثيم - zaki alkholy */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  a {
    @apply text-inherit no-underline;
  }
}

@layer components {
  .container {
    @apply w-full mx-auto px-4;
  }

  @media (min-width: 640px) {
    .container {
      @apply px-6;
    }
  }

  @media (min-width: 1024px) {
    .container {
      @apply px-8 max-w-7xl;
    }
  }
}

@layer utilities {
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-bounce-in {
    animation: bounceIn 1s ease-out forwards;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }

  .animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .hover-scale {
    transition: transform 0.3s ease;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.4);
    transform: translateY(-2px);
  }

  .hover-rotate {
    transition: transform 0.3s ease;
  }

  .hover-rotate:hover {
    transform: rotate(5deg) scale(1.05);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #374151;
  }

  /* Calendar modern styling */
  .calendar-modern .react-calendar {
    border: none !important;
    border-radius: 12px !important;
    font-family: "Cairo", sans-serif !important;
  }

  .calendar-modern .react-calendar__tile {
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
  }

  .calendar-modern .react-calendar__tile:hover {
    background: linear-gradient(135deg, #dbeafe, #e0e7ff) !important;
    transform: scale(1.05) !important;
  }

  .calendar-modern .react-calendar__tile--active {
    background: linear-gradient(135deg, #4f46e5, #8b5cf6) !important;
    color: white !important;
  }

  .dark .calendar-modern .react-calendar__tile:hover {
    background: linear-gradient(135deg, #1e40af, #7c3aed) !important;
  }

  /* Responsive utilities */
  @media (max-width: 640px) {
    .mobile-stack {
      flex-direction: column !important;
    }

    .mobile-full {
      width: 100% !important;
    }

    .mobile-text-sm {
      font-size: 0.875rem !important;
    }

    .mobile-p-4 {
      padding: 1rem !important;
    }

    .mobile-gap-2 {
      gap: 0.5rem !important;
    }
  }

  @media (max-width: 768px) {
    .tablet-stack {
      flex-direction: column !important;
    }

    .tablet-grid-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }
  }

  /* Enhanced mobile experience */
  @media (hover: none) and (pointer: coarse) {
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-glow:hover,
    .hover-rotate:hover {
      transform: none !important;
      box-shadow: none !important;
    }

    .mobile-touch-friendly {
      min-height: 44px !important;
      min-width: 44px !important;
    }
  }
}
/* ============================================================================================================================= */
/* ================================================Fonts=================================================================== */
/* ============================================================================================================================= */
/* Keyframes for animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(79, 70, 229, 0.2);
  }
  to {
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.6);
  }
}

@keyframes wiggle {
  0%,
  7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%,
  100% {
    transform: rotateZ(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

/* استخدام خط Tajawal للعناوين */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Tajawal", "Cairo", "Baloo Bhaijaan 2", sans-serif;
  font-weight: 700;
  font-style: normal;
}

/* استخدام خط Cairo للنصوص والفقرات */
body,
p,
span,
li,
a,
input,
textarea,
button {
  font-family: "Cairo", "Tajawal", sans-serif;
  font-weight: 400;
}

/* تحسينات عامة */
body {
  line-height: 1.8;
  font-size: 16px;
  color: #222;
  background-color: #fff;
  direction: rtl; /* لو الموقع بالعربي */
}

/* Custom Animations for Enhanced UI */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Light mode scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Additional animations for enhanced student profile */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-delayed {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes float-slow {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.animate-float-delayed {
  animation: float-delayed 4s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-float-slow {
  animation: float-slow 5s ease-in-out infinite;
  animation-delay: 2s;
}
