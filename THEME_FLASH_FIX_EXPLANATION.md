# إصلاح مشكلة تغيير الثيم أثناء التحميل (Theme Flash Fix)

## المشكلة الأصلية:
عند عمل refresh للصفحة، كان المستخدم يرى تغيير مفاجئ في الثيم أثناء التحميل، وهذا يحدث بسبب:

1. **تضارب في CSS Variables**: كانت القيم في `globals.css` مختلفة عن القيم في `ThemeContext.jsx`
2. **تأخير في تطبيق الثيم**: كان الثيم يتم تطبيقه بعد تحميل React، مما يسبب "flash"
3. **عدم تزامن**: كان هناك فترة زمنية بين تحميل CSS الأولي وتطبيق الثيم من JavaScript

## الحلول المطبقة:

### 1. توحيد CSS Variables
```javascript
// في ThemeContext.jsx - تم توحيد القيم مع globals.css
if (isDarkMode) {
  htmlElement.style.setProperty("--primary", "#6366f1"); // نفس القيمة في globals.css
  htmlElement.style.setProperty("--background", "#0a0a0a");
  // ... باقي المتغيرات
} else {
  htmlElement.style.setProperty("--primary", "#4f46e5"); // نفس القيمة في globals.css
  htmlElement.style.setProperty("--background", "#ffffff");
  // ... باقي المتغيرات
}
```

### 2. تطبيق الثيم الفوري في ThemeContext
```javascript
// تطبيق الثيم فوراً عند التحميل لمنع الـ flash
const htmlElement = document.documentElement;
if (shouldBeDark) {
  htmlElement.classList.add("dark");
} else {
  htmlElement.classList.remove("dark");
}
```

### 3. Script في Layout لتطبيق الثيم قبل React
```javascript
// في layout.jsx - script يعمل قبل تحميل React
<script dangerouslySetInnerHTML={{
  __html: `
    (function() {
      try {
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const shouldBeDark = savedTheme ? savedTheme === 'dark' : systemPrefersDark;
        
        if (shouldBeDark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      } catch (e) {
        console.warn('Could not access localStorage for theme');
      }
    })();
  `,
}} />
```

### 4. إضافة Transitions سلسة
```css
/* في globals.css */
html, body {
  transition: background-color 0.3s ease, color 0.3s ease;
}
```

## النتيجة:
- ✅ لا يوجد تغيير مفاجئ في الثيم أثناء التحميل
- ✅ الثيم يتم تطبيقه فوراً قبل ظهور المحتوى
- ✅ انتقال سلس بين الثيمات
- ✅ توافق كامل مع إعدادات النظام
- ✅ حفظ واسترجاع الثيم من localStorage

## الملفات المعدلة:
1. `src/contexts/ThemeContext.jsx` - توحيد CSS Variables وتطبيق فوري
2. `src/app/layout.jsx` - إضافة script للتطبيق المبكر
3. `src/app/globals.css` - إضافة transitions سلسة

## اختبار الحل:
1. افتح الموقع في الوضع الفاتح
2. غير إلى الوضع الداكن
3. اعمل refresh للصفحة
4. يجب أن يظهر الموقع مباشرة في الوضع الداكن بدون أي تغيير مفاجئ
